"use client";

import { useState } from "react";
import Loader from "@/components/Loader";
import { useChatContext } from "@/contexts/ChatContext";
import Link from "next/link";

// Import form components
import BotMessage from "@/components/functions/BotMessage";
import EmailInput from "@/components/functions/EmailInput";
import GenericInput from "@/components/functions/GenericInput";
import NameInput from "@/components/functions/NameInput";
import ScaleInput from "@/components/functions/ScaleInput";
import WebsiteInput from "@/components/functions/WebsiteInput";
import YesNoSelectInput from "@/components/functions/YesNo";

export default function FormArea(): JSX.Element {
  const { messages, sendMessage, status } = useChatContext();
  const [hasStarted, setHasStarted] = useState(false);

  const handleStart = (e: React.FormEvent) => {
    e.preventDefault();
    setHasStarted(true);
    sendMessage("start");
  };

  // Function to render UI components based on tool calls
  const renderToolComponent = (part: any) => {
    if (!part.output) return null;

    const { component, message } = part.output;

    switch (component) {
      case 'NameInput':
        return <NameInput key={part.id} message={message} />;
      case 'EmailInput':
        return <EmailInput key={part.id} message={message} />;
      case 'WebsiteInput':
        return <WebsiteInput key={part.id} message={message} />;
      case 'GenericInput':
        return <GenericInput key={part.id} message={message} />;
      case 'YesNoSelectInput':
        return <YesNoSelectInput key={part.id} message={message} />;
      case 'ScaleInput':
        return <ScaleInput key={part.id} message={message} />;
      case 'BotMessage':
        return <BotMessage key={part.id} message={message} />;
      default:
        return null;
    }
  };

  // Get the latest UI component from tool calls
  const getLatestUIComponent = () => {
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      if (message.role === 'assistant') {
        for (const part of message.parts) {
          if (part.type.startsWith('tool-') && part.state === 'output-available') {
            return renderToolComponent(part);
          }
        }
      }
    }
    return null;
  };

  return (
    <div className="bg-zinc-950 relative w-full h-full px-6 flex-col rounded-xl shadow-xl border border-zinc-200 overflow-hidden">
      {/* Video credit to https://www.pexels.com/@spacetraveler/ */}
      <video
        className="h-full w-full absolute object-cover left-0 opacity-70"
        autoPlay
        muted
        loop
        src="https://videos.pexels.com/video-files/1851190/1851190-uhd_3840_2160_25fps.mp4"
      ></video>
      {!hasStarted && (
        <form
          onSubmit={handleStart}
          className="text-white flex items-center flex-col justify-center h-full pb-20 z-20 relative"
        >
          <h1 className="text-6xl sm:text-8xl tracking-tighter font-semibold text-center max-w-4xl mx-auto text-balance">
            Generative UI with Vercel AI SDK
          </h1>
          <p className="text-xl opacity-70 text-center mt-4 mb-10 text-balance">
            An example showcasing generative UI using the Vercel AI SDK
          </p>
          <button
            className="bg-white hover:bg-opacity-70 transition duration-300 hover:scale-95 rounded-full py-2 px-6 text-black font-semibold"
            type="submit"
          >
            Get Started
          </button>
        </form>
      )}
      <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-auto min-h-[12rem] max-w-[40rem] px-6">
        <div className="relative pb-12">
          {status !== 'streaming' && getLatestUIComponent()}
          {status === 'streaming' && <Loader />}
        </div>
      </div>

      <Link
        target="_blank"
        rel="follow"
        href="https://vercel.com/ai"
        className="absolute bottom-4 left-4 text-center text-xs text-zinc-500 z-30 bg-zinc-600/40 p-3 rounded-xl group"
      >
        <span className="">Made with ❤️ using</span>{" "}
        <span className="font-semibold text-white group-hover:text-zinc-500">
          Vercel AI SDK
        </span>
      </Link>
    </div>
  );
}
