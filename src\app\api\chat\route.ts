import { google } from '@ai-sdk/google';
import { streamText, convertToModelMessages, UIMessage } from 'ai';
import { z } from 'zod';
import { tool as createTool } from 'ai';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Define tools for generative UI
const tools = {
  renderNameForm: createTool({
    description: 'Render a form to capture the full name of the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'NameInput',
        message,
      };
    },
  }),

  renderEmailForm: createTool({
    description: 'Render a form to capture the email of the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'EmailInput',
        message,
      };
    },
  }),

  renderWebsiteForm: createTool({
    description: 'Render a form to capture the website of the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'WebsiteInput',
        message,
      };
    },
  }),

  renderGenericForm: createTool({
    description: 'Render a generic form to capture input from the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'GenericInput',
        message,
      };
    },
  }),

  renderYesNoForm: createTool({
    description: 'Render a form to capture a yes or no answer from the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'YesNoSelectInput',
        message,
      };
    },
  }),

  renderScaleForm: createTool({
    description: 'Render a form to capture a number between 0 and 10 from the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'ScaleInput',
        message,
      };
    },
  }),

  formComplete: createTool({
    description: 'Complete the form with a thank you message.',
    inputSchema: z.object({
      message: z.string().describe('Thank you message to display'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'BotMessage',
        message,
      };
    },
  }),
};

export async function POST(request: Request) {
  try {
    // Validate environment variables
    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing API key configuration'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const { messages }: { messages: UIMessage[] } = await request.json();

    console.log('Chat API - Received messages:', messages.length);
    console.log('Chat API - Using model:', process.env.GOOGLE_MODEL || 'gemini-2.5-pro');

    const result = streamText({
      model: google(process.env.GOOGLE_MODEL || 'gemini-2.5-pro'),
      system: `You are a witty conversational assistant built with Vercel AI SDK. Your purpose is to collect information from users through an engaging conversation. You have access to UI functions to help you get information from the user. These functions start with the prefix "render". The functions will display interactive forms to the user where user input is expected. Always use these functions to get the required information from the user.

Here are the steps to follow:
1. To start the conversation, wait for the user to say "start". Once they do, you can begin by using renderNameForm.
2. Continue with the rest of the 'render' functions provided to you. Use all of them in a conversational manner.
3. At the end of the conversation use the "formComplete" function and point the user to https://github.com/vercel/ai for more information on Vercel AI SDK.

Here are the rules you must follow:

- Use the provided functions to render forms and collect information.
- Show your personality through the conversation with the user.
- Mix and match the order in which you present the functions to keep the form engaging.
- If the user fails to provide required information after 2-3 reminders, end the form by using the 'formComplete' function.
- Use the function 'renderGenericForm' to prompt the user for information that is not covered in pre-defined forms.
- Always acknowledge and confirm the user's input before moving to the next step.
- Be conversational and engaging throughout the process.

Remember, your purpose is to create an engaging and helpful conversation experience for the user.`,
      messages: convertToModelMessages(messages),
      tools,
    });

    console.log('Chat API - Stream created successfully');
    return result.toUIMessageStreamResponse();
  } catch (error) {
    console.error('Chat API - Error:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return new Response(
          JSON.stringify({
            error: 'Authentication error',
            details: 'Invalid or missing API key'
          }),
          {
            status: 401,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }

      if (error.message.includes('quota') || error.message.includes('rate limit')) {
        return new Response(
          JSON.stringify({
            error: 'Rate limit exceeded',
            details: 'Please try again later'
          }),
          {
            status: 429,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }
    }

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}
