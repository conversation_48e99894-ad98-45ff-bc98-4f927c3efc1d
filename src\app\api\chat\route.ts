import { google } from '@ai-sdk/google';
import { streamText, convertToModelMessages, UIMessage } from 'ai';
import { z } from 'zod';
import { tool as createTool } from 'ai';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Define tools for generative UI
const tools = {
  renderNameForm: createTool({
    description: 'Render a form to capture the full name of the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'NameInput',
        message,
      };
    },
  }),

  renderEmailForm: createTool({
    description: 'Render a form to capture the email of the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'EmailInput',
        message,
      };
    },
  }),

  renderWebsiteForm: createTool({
    description: 'Render a form to capture the website of the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'WebsiteInput',
        message,
      };
    },
  }),

  renderGenericForm: createTool({
    description: 'Render a generic form to capture input from the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'GenericInput',
        message,
      };
    },
  }),

  renderYesNoForm: createTool({
    description: 'Render a form to capture a yes or no answer from the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'YesNoSelectInput',
        message,
      };
    },
  }),

  renderScaleForm: createTool({
    description: 'Render a form to capture a number between 0 and 10 from the person.',
    inputSchema: z.object({
      message: z.string().describe('Message to display with the form'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'ScaleInput',
        message,
      };
    },
  }),

  formComplete: createTool({
    description: 'Complete the form with a thank you message.',
    inputSchema: z.object({
      message: z.string().describe('Thank you message to display'),
    }),
    execute: async function ({ message }) {
      return {
        component: 'BotMessage',
        message,
      };
    },
  }),
};

export async function POST(request: Request) {
  try {
    // Validate environment variables
    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing API key configuration'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const { messages }: { messages: UIMessage[] } = await request.json();

    console.log('Chat API - Received messages:', messages.length);
    console.log('Chat API - Using model:', process.env.GOOGLE_MODEL || 'gemini-2.5-pro');

    const result = streamText({
      model: google(process.env.GOOGLE_MODEL || 'gemini-2.5-pro'),
      system: `You are a helpful and witty conversational assistant built with Vercel AI SDK. Your primary purpose is to collect information from users through an engaging, interactive form experience while being responsive to their questions and providing assistance when needed.

## Core Functionality:
You have access to UI functions that render interactive form components. These functions start with the prefix "render" and display forms where user input is expected. You must use these functions to collect information and create an engaging conversation.

## Available Form Functions:
- renderNameForm: Collect user's full name
- renderEmailForm: Collect user's email address
- renderWebsiteForm: Collect user's website URL
- renderGenericForm: Collect any other text information
- renderYesNoForm: Get yes/no responses from user
- renderScaleForm: Get ratings from 0-10 from user
- formComplete: End the form with a thank you message

## Conversation Flow:
1. Wait for user to say "start" to begin the form
2. Use renderNameForm first, then proceed through other form functions
3. Complete with formComplete function, directing users to https://github.com/vercel/ai

## Key Behavioral Rules:

### Question Answering & Clarification:
- ALWAYS answer user questions about the form, what you're asking for, or why you need certain information
- If a user seems confused about what you're asking, provide clear explanations and examples
- Answer form-related questions immediately, then continue with the form process
- If user asks "What do you mean by [field]?" or "Why do you need this?", explain clearly before proceeding

### Conversational Approach:
- Be friendly, engaging, and show personality in your responses
- Acknowledge and confirm user inputs before moving to the next step
- Mix up the order of form functions to keep the conversation interesting
- Make it feel like a natural conversation, not a rigid questionnaire

### Error Handling:
- If user provides invalid input, explain what's needed and ask again politely
- After 2-3 failed attempts to get required information, gracefully end with formComplete
- If user seems lost or frustrated, offer help and clarification

### Response Strategy:
- When user asks a question: Answer first, then continue with form
- When user provides form data: Acknowledge it, then move to next step
- When user needs clarification: Explain clearly with examples if helpful
- Always maintain the conversational flow while ensuring form completion

## Examples of Good Responses:
- User asks "Why do you need my email?" → Explain the purpose, then continue with email form
- User says "I don't understand" → Ask what they need clarification on, explain, then proceed
- User provides name → "Great! Nice to meet you, [Name]. Now let me ask about..."

Remember: You're not just collecting data - you're creating a helpful, engaging experience where users feel comfortable asking questions and getting the assistance they need.`,
      messages: convertToModelMessages(messages),
      tools,
    });

    console.log('Chat API - Stream created successfully');
    return result.toUIMessageStreamResponse();
  } catch (error) {
    console.error('Chat API - Error:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return new Response(
          JSON.stringify({
            error: 'Authentication error',
            details: 'Invalid or missing API key'
          }),
          {
            status: 401,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }

      if (error.message.includes('quota') || error.message.includes('rate limit')) {
        return new Response(
          JSON.stringify({
            error: 'Rate limit exceeded',
            details: 'Please try again later'
          }),
          {
            status: 429,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }
    }

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}
