"use client";

import BotMessage from "@/components/functions/BotMessage";
import { useTypingAnimation } from "@/hooks/useTypeAnimation";
import { useChatContext } from "@/contexts/ChatContext";

export default function ScaleInput({ message }: { message: string }) {
  const { sendMessage } = useChatContext();
  const { isTyping } = useTypingAnimation(message);

  return (
    <>
      <BotMessage message={message} />

      {!isTyping && (
        <div className="flex items-center gap-1.5">
          {Array.from({ length: 11 }).map((_, index) => (
            <button
              key={index}
              onClick={(e) => {
                sendMessage(`I rate this ${index} out of 10`);
              }}
              className="text-white border border-zinc-500 rounded-lg h-16 w-16 flex items-center justify-center hover:bg-white/20"
            >
              {index}
            </button>
          ))}
        </div>
      )}
    </>
  );
}
