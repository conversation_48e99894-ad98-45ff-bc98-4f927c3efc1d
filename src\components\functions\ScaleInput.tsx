"use client";

import BotMessage from "@/components/functions/BotMessage";
import { useTypingAnimation } from "@/hooks/useTypeAnimation";
import { useChatContext } from "@/contexts/ChatContext";

export default function ScaleInput({ message }: { message: string }) {
  const { sendMessage } = useChatContext();
  const { isTyping } = useTypingAnimation(message);

  return (
    <>
      <BotMessage message={message} />

      {!isTyping && (
        <div className="flex items-center gap-2 justify-start flex-wrap">
          {Array.from({ length: 11 }).map((_, index) => (
            <button
              key={index}
              onClick={(e) => {
                sendMessage(`I rate this ${index} out of 10`);
              }}
              className="text-white border border-zinc-500 rounded-lg h-12 w-12 flex items-center justify-center hover:bg-white/20 text-sm"
            >
              {index}
            </button>
          ))}
        </div>
      )}
    </>
  );
}
