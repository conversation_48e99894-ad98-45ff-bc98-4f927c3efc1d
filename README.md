# AI Form Builder Example with Vercel AI SDK

This repository contains the code for an AI form builder. Built with the Vercel AI SDK and Google's Gemini model, this example showcases how to render UI components inside an AI conversation using generative UI.

## Technology Stack

- **Vercel AI SDK**: For building the chatbot logic and handling conversation flow with generative UI.
- **Google Gemini 2.5 Pro**: The AI model powering the conversational experience.
- **React**: For UI components that interact with the user, such as forms for capturing user details.
- **Next.js**: Full-stack React framework for the application.
- **TypeScript**: For type safety and better development experience.

## Setup

1. Ensure you have Node.js and Bun installed.
2. Clone this repository.
3. Install dependencies by running `bun install`.
4. Set the `GOOGLE_GENERATIVE_AI_API_KEY` environment variable with your Google AI API key.
   - Get your API key from: https://aistudio.google.com/app/apikey
5. Optionally, set the `GOOGLE_MODEL` environment variable to specify the model used for conversation (default is gemini-2.5-pro).

## Usage

Run the development server:

```bash
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

The chatbot uses several custom tools to interact with the user through generative UI. These include form components for capturing user information like name, email, website, and preferences.

## Features

- **Generative UI**: Dynamic form components rendered based on AI conversation flow
- **Multiple Input Types**: Name, email, website, generic text, yes/no choices, and scale ratings
- **Conversational Flow**: AI guides users through form completion in a natural way
- **Real-time Streaming**: Responses stream in real-time for better user experience

## Learn More

To learn more about Vercel AI SDK and related technologies:

- [Vercel AI SDK Documentation](https://sdk.vercel.ai/docs) - learn about the AI SDK
- [Google AI Studio](https://aistudio.google.com/) - get your API key and explore Gemini models
- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features

## Deployment

The easiest way to deploy this Next.js app is to use the [Vercel Platform](https://vercel.com).

## Contributing

Contributions to enhance the chatbot's functionality or address issues are welcome. Please follow the standard pull request process for contributions.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
