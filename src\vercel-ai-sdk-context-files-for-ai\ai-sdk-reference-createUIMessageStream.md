
# `createUIMessageStream`

The `createUIMessageStream` function allows you to create a readable stream for UI messages with advanced features like message merging, error handling, and finish callbacks.

## Import

<Snippet text={`import { createUIMessageStream } from "ai"`} prompt={false} />

## Example

```tsx
const existingMessages: UIMessage[] = [
  /* ... */
];

const stream = createUIMessageStream({
  async execute({ writer }) {
    // Write a message chunk
    writer.write({
      type: 'text-delta',
      textDelta: 'Hello',
    });

    // Merge another stream from streamText
    const result = streamText({
      model: openai('gpt-4o'),
      prompt: 'Write a haiku about AI',
    });

    writer.merge(result.toUIMessageStream());
  },
  onError: error => `Custom error: ${error.message}`,
  originalMessages: existingMessages,
  onFinish: ({ messages, isContinuation, responseMessage }) => {
    console.log('Stream finished with messages:', messages);
  },
});
```

## API Signature

### Parameters

<PropertiesTable
  content={[
    {
      name: 'execute',
      type: '(options: { writer: UIMessageStreamWriter }) => Promise<void> | void',
      description:
        'A function that receives a writer instance and can use it to write UI message chunks to the stream.',
      properties: [
        {
          type: 'UIMessageStreamWriter',
          parameters: [
            {
              name: 'write',
              type: '(part: UIMessageChunk) => void',
              description: 'Writes a UI message chunk to the stream.',
            },
            {
              name: 'merge',
              type: '(stream: ReadableStream<UIMessageChunk>) => void',
              description:
                'Merges the contents of another UI message stream into this stream.',
            },
            {
              name: 'onError',
              type: '(error: unknown) => string',
              description:
                'Error handler that is used by the stream writer for handling errors in merged streams.',
            },
          ],
        },
      ],
    },
    {
      name: 'onError',
      type: '(error: unknown) => string',
      description:
        'A function that handles errors and returns an error message string. By default, it returns the error message.',
    },
    {
      name: 'originalMessages',
      type: 'UIMessage[] | undefined',
      description:
        'The original messages. If provided, persistence mode is assumed and a message ID is provided for the response message.',
    },
    {
      name: 'onFinish',
      type: '(options: { messages: UIMessage[]; isContinuation: boolean; responseMessage: UIMessage }) => void | undefined',
      description:
        'A callback function that is called when the stream finishes.',
      properties: [
        {
          type: 'FinishOptions',
          parameters: [
            {
              name: 'messages',
              type: 'UIMessage[]',
              description: 'The updated list of UI messages.',
            },
            {
              name: 'isContinuation',
              type: 'boolean',
              description:
                'Indicates whether the response message is a continuation of the last original message, or if a new message was created.',
            },
            {
              name: 'responseMessage',
              type: 'UIMessage',
              description:
                'The message that was sent to the client as a response (including the original message if it was extended).',
            },
          ],
        },
      ],
    },
    {
      name: 'generateId',
      type: 'IdGenerator | undefined',
      description:
        'A function to generate unique IDs for messages. Uses the default ID generator if not provided.',
    },
  ]}
/>

### Returns

`ReadableStream<UIMessageChunk>`

A readable stream that emits UI message chunks. The stream automatically handles error propagation, merging of multiple streams, and proper cleanup when all operations are complete.
