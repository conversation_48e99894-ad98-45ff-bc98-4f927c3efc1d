// Simple test script to verify the API endpoint
// Run with: node test-api.js

const testMessages = [
  {
    id: 'test-1',
    role: 'user',
    parts: [{ type: 'text', text: 'start' }]
  }
];

async function testAPI() {
  try {
    console.log('Testing API endpoint...');
    
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messages: testMessages }),
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      return;
    }

    // For streaming responses, we'll just check if we get a response
    const reader = response.body?.getReader();
    if (reader) {
      console.log('✅ API endpoint is working - streaming response received');
      reader.cancel();
    } else {
      console.log('❌ No streaming response received');
    }

  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  testAPI();
}

module.exports = { testAPI };
