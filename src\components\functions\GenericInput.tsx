'use client'

import { useState } from 'react'

import BotMessage from '@/components/functions/BotMessage'
import Input from '@/components/Input'
import { useChatContext } from '@/contexts/ChatContext'

export default function GenericInput({ message }: { message: string }) {
  const [value, setValue] = useState('')
  const { sendMessage } = useChatContext()

  return (
    <>
      <BotMessage message={message} />
      <Input
        message={message}
        type="text"
        placeholder="Type something..."
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            sendMessage(value)
          }
        }}
      />
    </>
  )
}
