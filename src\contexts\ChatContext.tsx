'use client';

import { createContext, useContext, ReactNode } from 'react';
import { useChat } from '@ai-sdk/react';

interface ChatContextType {
  messages: any[];
  sendMessage: (message: string) => void;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  error: Error | undefined;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export function ChatProvider({ children }: { children: ReactNode }) {
  const { messages, sendMessage, status, error } = useChat();

  const handleSendMessage = (message: string) => {
    try {
      console.log('ChatContext - Sending message:', message);
      sendMessage({ role: 'user', parts: [{ type: 'text', text: message }] });
    } catch (err) {
      console.error('ChatContext - Error sending message:', err);
    }
  };

  // Log status changes for debugging
  console.log('ChatContext - Status:', status, 'Messages:', messages.length);

  if (error) {
    console.error('ChatContext - Error:', error);
  }

  return (
    <ChatContext.Provider value={{
      messages,
      sendMessage: handleSendMessage,
      status,
      error,
    }}>
      {children}
    </ChatContext.Provider>
  );
}

export function useChatContext() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
}
