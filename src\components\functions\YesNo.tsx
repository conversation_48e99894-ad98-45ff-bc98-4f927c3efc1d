"use client";

import BotMessage from "@/components/functions/BotMessage";
import { useTypingAnimation } from "@/hooks/useTypeAnimation";
import { useChatContext } from "@/contexts/ChatContext";

export default function YesNoSelectInput({ message }: { message: string }) {
  const { sendMessage } = useChatContext();
  const { isTyping } = useTypingAnimation(message);

  return (
    <>
      <BotMessage message={message} />
      {!isTyping && (
        <div className="flex gap-4 justify-center">
          <button
            onClick={(e) => {
              sendMessage("Yes");
            }}
            className="text-white hover:bg-white/20 border border-zinc-500 h-12 rounded-lg px-12 text-sm"
          >
            Yes
          </button>
          <button
            onClick={(e) => {
              sendMessage("No");
            }}
            className="text-white hover:bg-white/20 border border-zinc-500 h-12 rounded-lg px-12 text-sm"
          >
            No
          </button>
        </div>
      )}
    </>
  );
}
